"""
消息處理工具模組 - 提供通用的Discord消息處理功能
"""

import asyncio
import logging
import socket
import uuid
from typing import Callable, Optional
from urllib.parse import urlparse

import discord

from . import ai_service

# 統一的 User-Agent 標頭，用於避免被網站阻擋
DEFAULT_USER_AGENT = (
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
    "AppleWebKit/537.36 (KHTML, like Gecko) "
    "Chrome/91.0.4472.124 Safari/537.36"
)

# 設置日誌
logger = logging.getLogger(__name__)


async def _validate_and_get_image_data(
    image: discord.Attachment, request_id: str
) -> Optional[bytes]:
    """驗證並獲取上傳的圖像數據"""
    if image.size > 20 * 1024 * 1024:  # 20MB
        logger.warning("[%s] 圖像文件太大: %s 字節", request_id, image.size)
        raise ValueError("圖像文件太大。請上傳小於20MB的圖片。")

    if not image.content_type or not image.content_type.startswith("image/"):
        logger.warning("[%s] 不支援的文件類型: %s", request_id, image.content_type)
        raise ValueError(
            f"不支援的文件類型: {image.content_type}。"
            "請上傳圖片文件（JPEG、PNG、GIF、BMP、WebP）。"
        )

    try:
        image_data = await image.read()
        if len(image_data) < 10:
            raise ValueError("圖像數據太短")
        logger.info(
            "[%s] 讀取圖像: %s 字節, 類型: %s",
            request_id,
            len(image_data),
            image.content_type,
        )
        return image_data
    except Exception as e:
        logger.error("[%s] 讀取圖像失敗: %s", request_id, str(e))
        raise ValueError(f"讀取圖像文件時出錯: {str(e)}") from e


async def handle_interaction(
    interaction: discord.Interaction,
    processing_message: str,
    processor_func: Callable,
    image: Optional[discord.Attachment] = None,
    url: Optional[str] = None,
    user_prompt: Optional[str] = None,
):
    """處理圖像互動的簡化流程"""
    await interaction.response.defer(thinking=True)
    request_id = str(uuid.uuid4())
    logger.info(
        "創建新的請求 ID: %s 用戶: %s", request_id, interaction.user.display_name
    )
    response_message = await interaction.followup.send(processing_message, wait=True)

    image_data = None
    try:
        if image:
            image_data = await _validate_and_get_image_data(image, request_id)
        elif url:
            image_data = await _download_image_from_url(url)

        if not image_data and (image or url):
            error_msg = (
                "無法獲取或處理圖像。請確保上傳了有效的圖片或提供了有效的圖片URL。"
            )
            if url and not image:
                error_msg = (
                    "無法從提供的URL下載圖像。請確保URL有效且指向一個可訪問的圖片文件。"
                )
            elif image and not url:
                error_msg = "無法讀取上傳的圖片文件。請確保文件格式正確且未損壞。"
            raise ValueError(error_msg)

        if image_data:
            image_data = await _resize_image_if_needed(image_data)

        user_id = str(interaction.user.id) if interaction.user else None
        asyncio.create_task(
            processor_func(
                image_data, user_id, response_message, request_id, user_prompt
            )
        )

    except ValueError as e:
        await interaction.followup.edit_message(response_message.id, content=f"❌ {e}")
        logger.warning(
            "[%s] 處理互動失敗: %s (image: %s, url: %s)",
            request_id,
            e,
            image is not None,
            url is not None,
        )


async def _is_private_ip(hostname: str) -> bool:
    """檢查主機名是否解析為私有IP地址"""
    try:
        # 使用非阻塞方式解析IP
        ip_info = await asyncio.get_event_loop().getaddrinfo(hostname, None)
        ip_address = ip_info[0][4][0]

        # 檢查IPv4私有地址
        if ip_address.startswith(("10.", "192.168.")) or (
            ip_address.startswith("172.") and 16 <= int(ip_address.split(".")[1]) <= 31
        ):
            return True

        # 檢查本地回環地址
        if ip_address in ("127.0.0.1", "::1"):
            return True

        return False
    except socket.gaierror:
        logger.warning("無法解析主機名: %s", hostname)
        return True  # 無法解析時，視為不安全
    except Exception as e:
        logger.error("檢查IP地址時發生未知錯誤: %s, 主機名: %s", e, hostname)
        return True  # 出現異常時，視為不安全


async def _download_image_from_url(url: str) -> Optional[bytes]:
    """從URL下載圖像，並增加SSRF防護"""
    try:
        # 1. 協議驗證
        parsed_url = urlparse(url)
        if parsed_url.scheme not in ["http", "https"]:
            logger.warning("無效的URL協議: %s", parsed_url.scheme)
            return None

        # 2. SSRF防護：檢查IP地址是否為私有
        hostname = parsed_url.hostname
        if not hostname or await _is_private_ip(hostname):
            logger.warning("檢測到潛在的SSRF攻擊，目標主機名解析為私有IP: %s", hostname)
            return None

        # 直接下載圖像
        import aiohttp

        headers = {"User-Agent": DEFAULT_USER_AGENT}
        timeout = aiohttp.ClientTimeout(total=30)  # 30秒超時

        async with aiohttp.ClientSession() as session:
            logger.info("正在從URL下載圖像: %s", url)

            async with session.get(url, headers=headers, timeout=timeout) as response:
                if response.status != 200:
                    logger.warning(
                        "下載失敗，HTTP狀態碼: %s, URL: %s", response.status, url
                    )
                    return None

                # 驗證 Content-Type
                if not response.content_type or not response.content_type.startswith(
                    "image/"
                ):
                    logger.warning(
                        "無效的 Content-Type: %s, URL: %s", response.content_type, url
                    )
                    return None

                # 讀取圖像數據
                image_data = await response.read()

                if image_data:
                    logger.info("成功下載圖像: %s bytes", len(image_data))
                    return image_data
                else:
                    logger.warning("下載的圖像數據為空: %s", url)
                    return None

    except Exception as e:
        logger.error("從URL下載圖像時出錯: %s, URL: %s", e, url, exc_info=True)
        return None


async def _resize_image_if_needed(image_data: bytes) -> Optional[bytes]:
    """如果必要，壓縮圖像 (非阻塞版本)"""
    if not image_data:
        return None

    # 檢查大小是否超過5MB
    size_mb = len(image_data) / (1024 * 1024)
    if size_mb > 5:
        logger.info("圖像過大 (%.2fMB)，進行壓縮", size_mb)
        try:
            return await ai_service.resize_image(image_data, max_size=5 * 1024 * 1024)
        except Exception as e:
            logger.error("壓縮圖像失敗: %s", str(e))
            return image_data
    return image_data


async def process_text_interaction(
    interaction: discord.Interaction,
    processing_message: str,
    processor_func: Callable,
    prompt: str,
):
    """處理純文本互動的簡化流程"""
    # 移除 try-except 區塊，讓異常可以冒泡到全局錯誤處理器
    # 1. 延遲回應
    await interaction.response.defer(thinking=True)

    # 2. 建立請求ID用於日誌追蹤
    request_id = str(uuid.uuid4())
    logger.info(
        "創建新的文本請求 ID: %s 用戶: %s", request_id, interaction.user.display_name
    )

    # 3. 發送處理中的提示消息
    response_message = await interaction.followup.send(processing_message, wait=True)

    # 4. 獲取用戶資訊
    user_id = str(interaction.user.id) if interaction.user else None

    # 5. 啟動背景任務處理請求
    asyncio.create_task(processor_func(prompt, user_id, response_message, request_id))
