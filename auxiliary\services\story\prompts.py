# -*- coding: utf-8 -*-
import logging
import random
from enum import Enum
from functools import lru_cache
from typing import Any, Dict, List, Optional, Sequence, Tuple, TypedDict, Union

from .cleanup_strategies import apply_cleanup_strategy, get_cleanup_strategy

# 導入新的多卡片庫管理器
from .prompt_libraries import get_available_libraries, get_prompt_library
from .template_processor import process_card_content

logger = logging.getLogger(__name__)


@lru_cache(maxsize=256)
def _process_keywords_cached(keywords_tuple: Tuple[str, ...]) -> List[str]:
    """
    快取關鍵字處理，避免重複運算

    Args:
        keywords_tuple: 關鍵字元組（不可變，可快取）

    Returns:
        處理後的小寫關鍵字列表
    """
    return [
        kw.lower().strip()
        for kw in keywords_tuple
        if isinstance(kw, str) and kw.strip()
    ]


@lru_cache(maxsize=1024)
def _fast_keyword_search(content: str, keywords_tuple: <PERSON><PERSON>[str, ...]) -> bool:
    """
    高效關鍵字搜尋，使用快取和早期終止

    Args:
        content: 要搜尋的內容
        keywords_tuple: 關鍵字元組

    Returns:
        是否找到任何關鍵字
    """
    content_lower = content.lower()
    return any(keyword in content_lower for keyword in keywords_tuple)


class MessageRole(str, Enum):
    """訊息角色列舉，確保類型安全"""

    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


class HistoryMessage(TypedDict):
    """歷史訊息的精確類型定義"""

    role: str
    content: str
    status_block: Optional[str]
    world_info_id: Optional[str]


def _check_keyword_triggers(
    history: Sequence[Union[Dict[str, Any], HistoryMessage]], keywords: List[str]
) -> bool:
    """
    檢查最近兩對消息中是否包含任何指定的關鍵字
    將"角色最近的回復"和"你新發出的消息"視為"一對"消息

    Args:
        history: 歷史記錄列表
        keywords: 要檢查的關鍵字列表

    Returns:
        如果在最近兩對消息中找到任何關鍵字則返回True，否則返回False
    """
    # 早期返回：沒有關鍵字則默認觸發
    if not keywords:
        return True

    # 輸入驗證 - 使用更 Pythonic 的方式
    if not isinstance(history, list) or not isinstance(keywords, list):
        logger.warning(
            f"Invalid input types: history={type(history)}, keywords={type(keywords)}"
        )
        return False

    # 智能關鍵字處理 - 快取優化和早期終止
    keywords_lower = _process_keywords_cached(tuple(keywords))

    if not keywords_lower:
        return True  # 沒有有效關鍵字，默認觸發

    # 直接檢查最近4條消息（約等於兩對），避免複雜的配對邏輯
    # 限制檢查範圍，提升性能
    check_limit = min(4, len(history))
    if check_limit == 0:
        return False

    # 從最新開始檢查，一旦找到就立即返回（早期終止）
    keywords_tuple = tuple(keywords_lower)  # 避免重複轉換

    for i in range(check_limit):
        turn = history[-(i + 1)]  # 從後往前取

        # 構建完整內容
        content = str(turn.get("content", ""))
        status_block = turn.get("status_block")
        if status_block:
            content += f" {status_block}"

        if content and _fast_keyword_search(content, keywords_tuple):
            return True

    return False


def _inject_history_with_unified_cards(
    messages: List[Dict[str, str]],
    short_term_history: Sequence[Union[Dict[str, Any], HistoryMessage]],
    long_term_summaries: List[str],
    history_cards: List[Dict[str, Any]],
    format_kwargs: Dict[str, Any],
    library_name: str,
):
    """
    使用統一卡片結構注入歷史記錄。
    支持 insertion_method = "history" 的卡片，按 insertion_depth 和 order 排序注入。
    """
    # 0. 首先注入長期摘要作為歷史記錄格式
    strategy = get_cleanup_strategy(library_name)
    if long_term_summaries:
        for summary in long_term_summaries:
            # 將每個摘要作為assistant角色的歷史記錄注入
            cleaned_summary = apply_cleanup_strategy(
                summary, strategy, cleanup_type="history"
            )
            if cleaned_summary.strip():  # 只有清理後還有內容才注入
                _append_message(
                    messages, {"role": "assistant", "content": cleaned_summary}
                )

    # 如果沒有 history 類型的卡片，直接注入歷史記錄
    if not history_cards:
        history_len = len(short_term_history)
        for i, turn in enumerate(short_term_history):
            if turn.get("role") == "assistant":
                full_content = str(turn.get("content", ""))
                status_block = turn.get("status_block")
                if status_block:
                    full_content += f"\n<status_block>{status_block}</status_block>"
                content = apply_cleanup_strategy(
                    full_content, strategy, cleanup_type="history"
                )
                _append_message(messages, {"role": "assistant", "content": content})
            else:  # role == "user"
                user_content = str(turn.get("content", ""))
                is_last_turn = i == history_len - 1
                if library_name == "beilu" and is_last_turn:
                    user_content = f"<user_input>{user_content}</user_input>"
                _append_message(messages, {"role": "user", "content": user_content})
        return

    # 按 insertion_depth 分組 history 卡片
    cards_by_depth = {}
    for card in history_cards:
        depth = card.get("insertion_depth", 0)
        if not isinstance(depth, (int, float)):
            logger.warning(
                f"Invalid insertion_depth for card {card.get('id', 'unknown')}: {depth}, using 0"
            )
            depth = 0
        depth = int(depth)

        if depth not in cards_by_depth:
            cards_by_depth[depth] = []

        order = card.get("order", 0)
        if not isinstance(order, (int, float)):
            logger.warning(
                f"Invalid order for card {card.get('id', 'unknown')}: {order}, using 0"
            )
            order = 0

        cards_by_depth[depth].append(
            {
                "content": process_card_content(card["content"], format_kwargs),
                "role": card.get("role", "system"),
                "order": order,
            }
        )

    # 找到需要處理的最大深度
    max_depth = max(cards_by_depth.keys()) if cards_by_depth else -1
    history_len = len(short_term_history)

    # 處理超出歷史長度的深度（預先加載）
    for depth in range(max_depth, history_len - 1, -1):
        if depth in cards_by_depth:
            sorted_cards = sorted(cards_by_depth[depth], key=lambda x: x["order"])
            for card_info in sorted_cards:
                _append_message(
                    messages,
                    {"role": card_info["role"], "content": card_info["content"]},
                )

    # 注入歷史記錄，並在適當位置插入 history 卡片
    for i, turn in enumerate(short_term_history):
        # 先注入當前回合的歷史訊息
        if turn.get("role") == "assistant":
            full_content = str(turn.get("content", ""))
            status_block = turn.get("status_block")
            if status_block:
                full_content += f"\n<status_block>{status_block}</status_block>"
            content = apply_cleanup_strategy(
                full_content, strategy, cleanup_type="history"
            )
            _append_message(messages, {"role": "assistant", "content": content})
        else:  # role == "user"
            user_content = str(turn.get("content", ""))
            is_last_turn = i == history_len - 1
            if library_name == "beilu" and is_last_turn:
                user_content = f"<user_input>{user_content}</user_input>"
            _append_message(messages, {"role": "user", "content": user_content})

        # 檢查是否需要在這個歷史訊息之後插入對應深度的卡片
        depth_from_newest = history_len - 1 - i
        if depth_from_newest in cards_by_depth:
            sorted_cards = sorted(
                cards_by_depth[depth_from_newest], key=lambda x: x["order"]
            )
            for card_info in sorted_cards:
                _append_message(
                    messages,
                    {"role": card_info["role"], "content": card_info["content"]},
                )


def build_prompt_messages(
    theme_settings: Dict[str, Any],
    long_term_summaries: List[str],
    short_term_history: Sequence[Union[Dict[str, Any], HistoryMessage]],
    user_input: str,
    user_display_name: str,
    library_name: Optional[str] = None,
) -> List[Dict[str, str]]:
    """
    使用統一的卡片結構，動態構建完整的 messages 列表。
    支持新的 insertion_method 統一處理機制。
    """
    messages = []
    if library_name is None:
        library_name = theme_settings.get("prompt_library")
        if not library_name:
            raise ValueError("...")

    # 獲取的是一個副本，可以安全修改
    prompt_library = [card.copy() for card in get_prompt_library(library_name)]

    # 準備格式化變數
    format_kwargs = {
        "user_display_name": user_display_name,
        "character_sheet": theme_settings.get("character_sheet", ""),
        "user_input": user_input,
        "status_block_rules": theme_settings.get("status_block_rules", ""),
        "char": theme_settings.get("title", "角色"),
    }

    # 統一處理所有卡片：從 prompt_library 和 theme 的 prompt_cards 合併
    all_cards = []

    # 添加全局 prompt_library 的卡片
    all_cards.extend(prompt_library)

    # 添加主題特定的 prompt_cards
    theme_cards = theme_settings.get("prompt_cards", [])
    all_cards.extend(theme_cards)

    # 同時處理舊格式的 custom_world_info_rules（向下兼容）
    legacy_rules = theme_settings.get("custom_world_info_rules", [])
    for rule in legacy_rules:
        if not rule.get("enabled", True):
            continue
        # 轉換舊格式為新格式
        converted_card = {
            "id": rule["id"],
            "role": rule.get("role", "system"),
            "content": rule["content"],
            "enabled": True,
            "trigger_probability": rule.get("trigger_probability", 1.0),
            "order": rule.get("order", 0),
        }

        # 轉換 insertion_target 為 insertion_method
        if rule.get("insertion_target") == "prompt_library":
            converted_card["insertion_method"] = "variable"
            converted_card["variable_name"] = rule.get(
                "insertion_id", "world_info_content"
            )
        elif rule.get("insertion_target") == "history":
            converted_card["insertion_method"] = "history"
            converted_card["insertion_depth"] = rule.get("insertion_depth", 0)
        else:
            # 默認為 variable 方式
            converted_card["insertion_method"] = "variable"
            converted_card["variable_name"] = "world_info_content"

        if "trigger_keywords" in rule:
            converted_card["trigger_keywords"] = rule["trigger_keywords"]

        all_cards.append(converted_card)

    # 分離不同 insertion_method 的卡片
    variable_cards = []  # insertion_method = "variable"
    history_cards = []  # insertion_method = "history"
    sequence_cards = []  # insertion_method = "sequence" (普通卡片)

    # 為關鍵字觸發準備歷史記錄（包含當前用戶輸入）
    full_history_for_trigger = list(short_term_history) + [
        {"role": "user", "content": user_input}
    ]

    for card in all_cards:
        if not card.get("enabled", True):
            continue

        # 檢查觸發概率
        trigger_prob = card.get("trigger_probability", 1.0)
        if not isinstance(trigger_prob, (int, float)) or not (0 <= trigger_prob <= 1):
            logger.warning(
                f"Invalid trigger_probability for card {card.get('id', 'unknown')}: {trigger_prob}, using 1.0"
            )
            trigger_prob = 1.0

        if random.random() > trigger_prob:
            continue

        # 檢查關鍵字觸發條件
        trigger_keywords = card.get("trigger_keywords", [])
        if trigger_keywords:
            if not isinstance(trigger_keywords, list):
                logger.warning(
                    f"Invalid trigger_keywords for card {card.get('id', 'unknown')}: {trigger_keywords}"
                )
                continue
            if not _check_keyword_triggers(full_history_for_trigger, trigger_keywords):
                continue

        # 根據 insertion_method 分類
        insertion_method = card.get("insertion_method", "sequence")
        if insertion_method == "variable":
            variable_cards.append(card)
        elif insertion_method == "history":
            history_cards.append(card)
        else:  # "sequence" 或其他值都當作普通序列卡片
            sequence_cards.append(card)

    # 處理 variable 類型的卡片，注入到 format_kwargs
    variable_groups = {}
    for card in variable_cards:
        variable_name = card.get("variable_name", "world_info_content")
        if variable_name not in variable_groups:
            variable_groups[variable_name] = []

        order = card.get("order", 0)
        if not isinstance(order, (int, float)):
            logger.warning(
                f"Invalid order for card {card.get('id', 'unknown')}: {order}, using 0"
            )
            order = 0

        variable_groups[variable_name].append(
            {"content": card["content"], "order": order}
        )

    # 將分組後的 variable 內容按順序排列並加入 format_kwargs
    for variable_name, items in variable_groups.items():
        sorted_items = sorted(items, key=lambda x: x["order"])
        contents = [item["content"] for item in sorted_items]
        combined_content = "\n".join(contents)
        format_kwargs[variable_name] = combined_content

    # 準備增強版歷史記錄（包含當前用戶輸入）
    augmented_history = list(short_term_history) + [
        {"role": "user", "content": user_input}
    ]

    # 處理 sequence 類型的卡片（普通序列卡片）
    for card in sequence_cards:
        # 處理條件檢查
        condition = card.get("condition")
        if condition:
            try:
                cond_type, cond_value = condition.split(":", 1)
                if (
                    cond_type == "theme_type"
                    and theme_settings.get("status_block_type") != cond_value
                ):
                    continue
            except ValueError:
                logger.warning(
                    "卡片條件格式無效，已跳過 - 卡片ID: %s, 條件: %s",
                    card.get("id", "unknown"),
                    condition,
                )
                continue

        if card["role"] == "marker":
            if card["id"] == "history_marker":
                # 注入歷史記錄和 history 類型的卡片
                _inject_history_with_unified_cards(
                    messages,
                    augmented_history,
                    long_term_summaries,
                    history_cards,
                    format_kwargs,
                    library_name,
                )
            continue  # Marker 卡片處理完後，跳過後續步驟

        # 處理普通卡片
        content = process_card_content(card["content"], format_kwargs)
        _append_message(messages, {"role": card["role"], "content": content})

    return messages


def _append_message(messages: List[Dict[str, str]], new_message: Dict[str, str]):
    """
    一個輔助函數，用於將新訊息添加到列表中。
    根據全域設定，此版本永不合併相同角色的訊息。
    """
    messages.append(new_message)


def get_available_prompt_libraries() -> List[str]:
    """
    獲取所有可用的提示詞卡片庫名稱列表

    Returns:
        可用卡片庫名稱列表
    """
    return get_available_libraries()


def get_prompt_library_info() -> Dict[str, Dict[str, Any]]:
    """
    獲取所有卡片庫的詳細信息

    Returns:
        包含所有卡片庫信息的字典
    """
    from .prompt_libraries import list_libraries_info

    return list_libraries_info()
